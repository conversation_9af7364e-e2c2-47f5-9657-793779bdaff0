﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.3.2
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public Guid StrategyId { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<TransmitMalariaVariable> TransmitMalariaVariables { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListVariablesCount { get; set; }

        /// <summary>
        /// Validates indicator 3.3.2
        /// </summary>
        /// <returns>Validation results for indicator 3.3.2</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Prepares analytical report along with the variable for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>    
        /// <param name="indicatorSequence">Indicator sequence</param>     
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Indicator 3.3.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null)
        {
            DataSet ds = new DataSet();

            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<TransmitMalariaVariableReport>();

            int variableCount = 1;

            TransmitMalariaVariables.ForEach(variable =>
            {
                string variableName = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId).Name;

                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(
                    variableCount,
                    variableName,
                    translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(), // Under5 - using Disagregation as proxy since Under5 property doesn't exist in this model
                    translator.DynamicInvoke(variable.Over5.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.PregnantWomen.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Other.ConvertBoolToYesNo()).ToString()
                ));

                variableCount++;
            });

            transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooterTitle_3_3_2).ToString(), GetFooterPercentage(TransmitMalariaVariables.Count(indicator => indicator.RecordedInSource == true), TransmitMalariaVariables.Count)));

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, indicatorSequence, translator);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Analytical output indicator response of excel export for indicator 3.3.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null)
        {
            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<TransmitMalariaVariableReport>();

            int variableCount = 1;

            TransmitMalariaVariables.ForEach(variable =>
            {
                string variableName = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId).Name;

                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(
                    variableCount,
                    variableName,
                    translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(), // Under5 - using Disagregation as proxy since Under5 property doesn't exist in this model
                    translator.DynamicInvoke(variable.Over5.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.PregnantWomen.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Other.ConvertBoolToYesNo()).ToString()
                ));

                variableCount++;
            });


            transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooterTitle_3_3_2).ToString(), GetFooterPercentage(TransmitMalariaVariables.Count(indicator => indicator.RecordedInSource == true), TransmitMalariaVariables.Count)));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        private string GetFooterPercentage(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(denominator, numerator);

            return $"{percentage}%";
        }
    }

    /// <summary>
    /// Contains details of Transmit Malaria Variables
    /// </summary>
    public class TransmitMalariaVariable
    {
        public Guid VariableId { get; set; }

        public bool? RecordedInSource { get; set; }

        public bool? Disagregation { get; set; }

        public bool? Over5 { get; set; }

        public bool? Gender { get; set; }

        public bool? PregnantWomen { get; set; }

        public bool? HealthSector { get; set; }

        public bool? Geography { get; set; }

        public bool? Other { get; set; }

    }

    /// <summary>
    /// Contains details of transmit malaria variables report
    /// </summary>
    public class TransmitMalariaVariableReport
    {
        [TableColumn(Name = "VariableCount", TranslationKey = "Common.SrNo", Width = Common.Width100, Order = 1)]
        public int? VariableCount { get; set; }

        [TableColumn(Name = "VariableName", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Variables", Width = Common.Width300, Order = 2)]
        public string VariableName { get; set; }

        [TableColumn(Name = "RecordedInSource", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.RecordedInSourceDocuments", Width = Common.Width300, Order = 3)]
        public string RecordedInSource { get; set; }

        [TableColumn(Name = "Disagregation", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Disagregation", Width = Common.Width150, Order = 4)]
        public string Disagregation { get; set; }

        [TableColumn(Name = "Under5", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Under5", Width = Common.Width150, Order = 5)]
        public string Under5 { get; set; }

        [TableColumn(Name = "Over5", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Over5", Width = Common.Width150, Order = 6)]
        public string Over5 { get; set; }

        [TableColumn(Name = "Sex", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Sex", Width = Common.Width150, Order = 7)]
        public string Sex { get; set; }

        [TableColumn(Name = "PregnantWoman", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.PregnantWoman", Width = Common.Width150, Order = 8)]
        public string PregnantWoman { get; set; }

        [TableColumn(Name = "HealthSector", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.HealthSector", Width = Common.Width200, Order = 9)]
        public string HealthSector { get; set; }

        [TableColumn(Name = "Geography", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Geography", Width = Common.Width150, Order = 10)]
        public string Geography { get; set; }

        [TableColumn(Name = "Other", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.Other", Width = Common.Width150, Order = 11)]
        public string Other { get; set; }

        public TransmitMalariaVariableReport(int? variableCount, string variableName, string recordedInSource,
            string disagregation = null, string under5 = null, string over5 = null, string sex = null,
            string pregnantWoman = null, string healthSector = null, string geography = null, string other = null)
        {
            VariableCount = variableCount;
            VariableName = variableName;
            RecordedInSource = recordedInSource;
            Disagregation = disagregation;
            Under5 = under5;
            Over5 = over5;
            Sex = sex;
            PregnantWoman = pregnantWoman;
            HealthSector = healthSector;
            Geography = geography;
            Other = other;
        }
    }
}
