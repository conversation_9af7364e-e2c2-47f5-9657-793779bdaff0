﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_3_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.3.2
    /// </summary>
    public class Response_2 : AssessmentResponseBase
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public bool? HasPVivaxCases { get; set; }

        public bool? HasMalariaInpatients { get; set; }

        public List<TransmitMalariaVariable> TransmitMalariaVariables { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListVariablesCount { get; set; }

        /// <summary>
        /// Validates indicator 3.3.2
        /// </summary>
        /// <returns>Validation results for indicator 3.3.2</returns>
        public ValidationResult Validate()
        {
            return new Response_2_Validator().Validate(this);
        }

        /// <summary>
        /// Prepares analytical report along with the variable for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>         
        /// <param name="indicatorSequence">Indicator sequence</param>   
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Indicator 3.3.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null)
        {
            DataSet ds = new DataSet();

            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<TransmitMalariaVariableReport>();

            int variableCount = 1;

            TransmitMalariaVariables.ForEach(variable =>
            {
                string variableName = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId).Name;

                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(
                    variableCount,
                    variableName,
                    translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(), // Under5 - using Disagregation as proxy since Under5 property doesn't exist in this model
                    translator.DynamicInvoke(variable.Over5.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.PregnantWomen.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Other.ConvertBoolToYesNo()).ToString()
                ));

                variableCount++;
            });

            transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooterTitle_3_3_2).ToString(), GetFooterPercentage(TransmitMalariaVariables.Count(indicator => indicator.RecordedInSource == true), TransmitMalariaVariables.Count)));

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, indicatorSequence, translator);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Analytical output indicator response of excel export for indicator 3.3.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null)
        {
            List<TransmitMalariaVariableReport> transmitMalariaVariableReports = new List<TransmitMalariaVariableReport>();

            int variableCount = 1;

            TransmitMalariaVariables.ForEach(variable =>
            {
                string variableName = drVariableCheckLists.First(dr => dr.VariableId == variable.VariableId).Name;

                transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(
                    variableCount,
                    variableName,
                    translator.DynamicInvoke(variable.RecordedInSource.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Disagregation.ConvertBoolToYesNo()).ToString(), // Under5 - using Disagregation as proxy since Under5 property doesn't exist in this model
                    translator.DynamicInvoke(variable.Over5.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.PregnantWomen.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(variable.Other.ConvertBoolToYesNo()).ToString()
                ));
                variableCount++;
            });

            transmitMalariaVariableReports.Add(new TransmitMalariaVariableReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooterTitle_3_3_2).ToString(), GetFooterPercentage(TransmitMalariaVariables.Count(indicator => indicator.RecordedInSource == true), TransmitMalariaVariables.Count)));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;
            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TransmitMalariaVariableReport), transmitMalariaVariableReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        private string GetFooterPercentage(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(denominator, numerator);

            return $"{percentage}%";
        }

        /// <summary>
        /// Contains details of Transmit Malaria Variables
        /// </summary>
        public class TransmitMalariaVariable
        {
            public Guid VariableId { get; set; }

            public bool? RecordedInSource { get; set; }

            public bool? Disagregation { get; set; }

            public bool? Over5 { get; set; }

            public bool? Gender { get; set; }

            public bool? PregnantWomen { get; set; }

            public bool? HealthSector { get; set; }

            public bool? Geography { get; set; }

            public bool? Other { get; set; }

            public byte? CaseCategory { get; set; }
        }
    }
}
