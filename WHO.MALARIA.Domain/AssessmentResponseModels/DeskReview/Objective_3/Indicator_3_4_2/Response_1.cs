﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.4.2
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<TransmitMalariaIndicator> TransmitMalariaIndicators { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListIndicatorsCount { get; set; }

        public bool? HasPVivaxCases { get; set; }

        /// <summary>
        /// Validates indicator 3.4.2
        /// </summary>
        /// <returns>Validation results for indicator 3.4.2</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Prepares analytical report along with the variable for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <param name="indicatorSequence">Indicator sequence</param>     
        ///<param name="drIndicatorCheckLists">Desk review indicator check list</param>  
        /// <returns>Indicator 3.4.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists)
        {
            DataSet ds = new DataSet();

            List<TransmitMalariaIndicatorReport> transmitMalariaIndicatorReports = new List<TransmitMalariaIndicatorReport>();

            int indicatorCount = 1;

            TransmitMalariaIndicators.ForEach(indicator =>
              {
                  string indicatorName = drIndicatorCheckLists.First(dr => dr.Id == indicator.ChecklistIndicatorId).Name;

                  transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(
                      indicatorCount,
                      indicatorName,
                      translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(), // Disagregation - using IndicatorMonitored as proxy
                      translator.DynamicInvoke(indicator.UnderFive.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.OverFive.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.Gender.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.PregnantWoman.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.HealthSector.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.Geography.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.ConfirmationMethod.ConvertBoolToYesNo()).ToString(),
                      translator.DynamicInvoke(indicator.Other.ConvertBoolToYesNo()).ToString()
                  ));

                  indicatorCount++;
              });
            transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooter_3_4_2).ToString(), GetFooterPercentage(TransmitMalariaIndicators.Count(indicator => indicator.IndicatorMonitored == true), TransmitMalariaIndicators.Count)));

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TransmitMalariaIndicatorReport), transmitMalariaIndicatorReports, indicatorSequence, translator);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drIndicatorCheckLists">Desk review indicator check list</param>  
        /// <returns>Analytical output indicator response of excel export for indicator 3.4.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists)
        {
            List<TransmitMalariaIndicatorReport> transmitMalariaIndicatorReports = new List<TransmitMalariaIndicatorReport>();

            int indicatorCount = 1;

            TransmitMalariaIndicators.ForEach(indicator =>
            {
                string indicatorName = drIndicatorCheckLists.First(dr => dr.Id == indicator.ChecklistIndicatorId).Name;

                transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(
                    indicatorCount,
                    indicatorName,
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(), // Disagregation - using IndicatorMonitored as proxy
                    translator.DynamicInvoke(indicator.UnderFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.OverFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.PregnantWoman.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.ConfirmationMethod.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Other.ConvertBoolToYesNo()).ToString()
                ));

                indicatorCount++;
            });

            transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooter_3_4_2).ToString(), GetFooterPercentage(TransmitMalariaIndicators.Count(t => t.IndicatorMonitored == true), TransmitMalariaIndicators.Count)));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TransmitMalariaIndicatorReport), transmitMalariaIndicatorReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        private string GetFooterPercentage(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(denominator, numerator);

            return $"{percentage}%";
        }
    }

    /// <summary>
    /// Contains details of Expected Output Malaria Indicator
    /// </summary>
    public class TransmitMalariaIndicator
    {
        public Guid ChecklistIndicatorId { get; set; }

        public bool? IndicatorMonitored { get; set; }

        public bool? UnderFive { get; set; }

        public bool? OverFive { get; set; }

        public bool? Gender { get; set; }

        public bool? PregnantWoman { get; set; }

        public bool? HealthSector { get; set; }

        public bool? Geography { get; set; }

        public bool? ConfirmationMethod { get; set; }

        public bool? Other { get; set; }
    }

    /// <summary>
    /// Contains details of expected output malaria indicator for report
    /// </summary>
    public class TransmitMalariaIndicatorReport
    {
        [TableColumn(Name = "ChecklistIndicatorCount", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.No.", Width = Common.Width100, Order = 1)]
        public int? ChecklistIndicatorCount { get; set; }

        [TableColumn(Name = "ChecklistIndicatorName", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Indicators", Width = Common.Width300, Order = 2)]
        public string ChecklistIndicatorName { get; set; }

        [TableColumn(Name = "IndicatorMonitored", TranslationKey = "DRObjective_3_Responses.Indicator_3_3_2.IndicatorMonitoredInRoutineOutputs", Width = Common.Width300, Order = 3)]
        public string IndicatorMonitored { get; set; }

        [TableColumn(Name = "Disagregation", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Disagregation", Width = Common.Width150, Order = 4)]
        public string Disagregation { get; set; }

        [TableColumn(Name = "Under5", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Under5", Width = Common.Width150, Order = 5)]
        public string Under5 { get; set; }

        [TableColumn(Name = "Over5", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Over5", Width = Common.Width150, Order = 6)]
        public string Over5 { get; set; }

        [TableColumn(Name = "Sex", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Sex", Width = Common.Width150, Order = 7)]
        public string Sex { get; set; }

        [TableColumn(Name = "PregnantWoman", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.PregnantWoman", Width = Common.Width150, Order = 8)]
        public string PregnantWoman { get; set; }

        [TableColumn(Name = "HealthSector", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.HealthSector", Width = Common.Width200, Order = 9)]
        public string HealthSector { get; set; }

        [TableColumn(Name = "Geography", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Geography", Width = Common.Width150, Order = 10)]
        public string Geography { get; set; }

        [TableColumn(Name = "MethodOfConfirmation", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.MethodOfConfirmation", Width = Common.Width200, Order = 11)]
        public string MethodOfConfirmation { get; set; }

        [TableColumn(Name = "Other", TranslationKey = "DRObjective_3_Responses.Indicator_3_4_2.Other", Width = Common.Width150, Order = 12)]
        public string Other { get; set; }

        public TransmitMalariaIndicatorReport(int? checklistIndicatorCount, string checklistIndicatorName, string indicatorMonitored,
            string disagregation = null, string under5 = null, string over5 = null, string sex = null,
            string pregnantWoman = null, string healthSector = null, string geography = null,
            string methodOfConfirmation = null, string other = null)
        {
            ChecklistIndicatorCount = checklistIndicatorCount;
            ChecklistIndicatorName = checklistIndicatorName;
            IndicatorMonitored = indicatorMonitored;
            Disagregation = disagregation;
            Under5 = under5;
            Over5 = over5;
            Sex = sex;
            PregnantWoman = pregnantWoman;
            HealthSector = healthSector;
            Geography = geography;
            MethodOfConfirmation = methodOfConfirmation;
            Other = other;
        }
    }

}