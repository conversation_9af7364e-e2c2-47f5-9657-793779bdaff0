﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2
{
    /// <summary>
    /// Contains desk review response properties for Indicator 3.4.2
    /// </summary>
    public class Response_3 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public List<TransmitMalariaIndicator> TransmitMalariaIndicators { get; set; }

        // property is used for checklist variables count and compare it with malariaVariables arrayOfObject count in validation rules
        public int CheckListIndicatorsCount { get; set; }

        public bool? HasPVivaxCases { get; set; }

        /// <summary>
        /// Validates indicator 3.4.2
        /// </summary>
        /// <returns>Validation results for indicator 3.4.2</returns>
        public ValidationResult Validate()
        {
            return new Response_3_Validator().Validate(this);
        }

        /// <summary>
        /// Prepares analytical report along with the variable for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>  
        /// <param name="indicatorSequence">Indicator sequence</param>     
        ///<param name="drIndicatorCheckLists">Desk review indicator check list</param>  
        /// <returns>Indicator 3.4.2 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence, IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists)
        {
            DataSet ds = new DataSet();

            List<TransmitMalariaIndicatorReport> transmitMalariaIndicatorReports = new List<TransmitMalariaIndicatorReport>();

            int indicatorCount = 1;

            TransmitMalariaIndicators.ForEach(indicator =>
            {
                string indicatorName = drIndicatorCheckLists.First(dr => dr.Id == indicator.ChecklistIndicatorId).Name;

                transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(
                    indicatorCount,
                    indicatorName,
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(), // Disagregation - using IndicatorMonitored as proxy
                    translator.DynamicInvoke(indicator.UnderFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.OverFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.PregnantWoman.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.ConfirmationMethod.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Other.ConvertBoolToYesNo()).ToString()
                ));

                indicatorCount++;
            });

            transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooter_3_4_2).ToString(), GetFooterPercentage(TransmitMalariaIndicators.Count(indicator => indicator.IndicatorMonitored == true), TransmitMalariaIndicators.Count)));

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TransmitMalariaIndicatorReport), transmitMalariaIndicatorReports, indicatorSequence, translator);

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        ///<param name="drIndicatorCheckLists">Desk review indicator check list</param>  
        /// <returns>Analytical output indicator response of excel export for indicator 3.4.2</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists)
        {
            List<TransmitMalariaIndicatorReport> transmitMalariaIndicatorReports = new List<TransmitMalariaIndicatorReport>();

            int indicatorCount = 1;

            TransmitMalariaIndicators.ForEach(indicator =>
            {
                string indicatorName = drIndicatorCheckLists.First(dr => dr.Id == indicator.ChecklistIndicatorId).Name;

                transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(
                    indicatorCount,
                    indicatorName,
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.IndicatorMonitored.ConvertBoolToYesNo()).ToString(), // Disagregation - using IndicatorMonitored as proxy
                    translator.DynamicInvoke(indicator.UnderFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.OverFive.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Gender.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.PregnantWoman.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.HealthSector.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Geography.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.ConfirmationMethod.ConvertBoolToYesNo()).ToString(),
                    translator.DynamicInvoke(indicator.Other.ConvertBoolToYesNo()).ToString()
                ));

                indicatorCount++;
            });

            transmitMalariaIndicatorReports.Add(new TransmitMalariaIndicatorReport(null, translator.DynamicInvoke(AnalyticalOutputConstants.TableFooter_3_4_2).ToString(), GetFooterPercentage(TransmitMalariaIndicators.Count(indicator => indicator.IndicatorMonitored == true), TransmitMalariaIndicators.Count)));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse trasmitMalariaVariableTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TransmitMalariaIndicatorReport), transmitMalariaIndicatorReports, translator);
            trasmitMalariaVariableTable.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = trasmitMalariaVariableTable
            };

            return response;
        }

        private string GetFooterPercentage(int numerator, int denominator)
        {
            int percentage = AnalyticalOutputHelper.CalculatePercentage(denominator, numerator);

            return $"{percentage}%";
        }
    }
}