﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_2.Response_2
{
    /// <summary>
    /// Contains validation rules for indicator 2.1.2
    /// </summary>
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        public Response_2_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
                .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' check box is not checked
            When(x => x.CannotBeAssessed == false, () =>
            {
                #region Public
                // RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria).NotNull();

                //RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.MandatedToReport).NotNull();

                //When(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true, () =>
                //{
                //    RuleFor(x => x.HealthSector.PublicHealthSector)
                //    .Must(z => z.HealthFacility.ReportingMalaria == true
                //           || z.Hospital.ReportingMalaria == true
                //           || z.Laboratory.ReportingMalaria == true)
                //    .WithMessage("At least one facility within the Public sector should be selected as 'Yes'");
                //});

                //When(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria.GetValueOrDefault() == false, () =>
                //{
                //    RuleFor(x => x.HealthSector.PublicHealthSector)
                //    .Must(z => z.HealthFacility.ReportingMalaria.GetValueOrDefault() == false
                //               && z.Hospital.ReportingMalaria.GetValueOrDefault() == false
                //               && z.Laboratory.ReportingMalaria.GetValueOrDefault() == false)
                //    .WithMessage("Please select 'Yes' for the Public health sector as decedent health systems are marked as 'Yes' else select 'No' for all descendent health systems");
                //});

                //RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.National).NotEmpty().When(z => z.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true);
                //RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.SubNational).NotEmpty().When(z => z.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true);
                //RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.PublicReportingDetails.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true);

                // Commenting below rules as client requested - number input fields should be optional
                // RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.National).NotEmpty().When(z => z.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.SubNational).NotEmpty().When(z => z.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.HealthFacility.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.National).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.SubNational).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Hospital.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Hospital.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.National).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.SubNational).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PublicHealthSector.Laboratory.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria == true);
                #endregion

                #region Private formal
                //  RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Hospital.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Military.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Police.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Prison.ReportingMalaria).NotNull();

                //RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Hospital.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Military.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Police.MandatedToReport).NotNull();
                RuleFor(x => x.HealthSector.PrivateFormal.Prison.MandatedToReport).NotNull();

                //When(x => x.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true, () =>
                //{
                //    RuleFor(x => x.HealthSector.PrivateFormal)
                //    .Must(z => z.HealthFacility.ReportingMalaria == true
                //              || z.Hospital.ReportingMalaria == true
                //              || z.Laboratory.ReportingMalaria == true
                //              || z.FaithBasedClinic.ReportingMalaria == true
                //              || z.NGOClinic.ReportingMalaria == true
                //              || z.Military.ReportingMalaria == true
                //              || z.Police.ReportingMalaria == true
                //              || z.Prison.ReportingMalaria == true)
                //    .WithMessage("At least one facility within the Private formal sector should be selected as 'Yes'");
                //});

                //When(x => x.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria.GetValueOrDefault() == false, () =>
                //{
                //    RuleFor(x => x.HealthSector.PrivateFormal)
                //   .Must(z => z.HealthFacility.ReportingMalaria == false
                //                  && z.Hospital.ReportingMalaria == false
                //                  && z.Laboratory.ReportingMalaria == false
                //                  && z.FaithBasedClinic.ReportingMalaria == false
                //                  && z.NGOClinic.ReportingMalaria == false
                //                  && z.Military.ReportingMalaria == false
                //                  && z.Police.ReportingMalaria == false
                //                  && z.Prison.ReportingMalaria == false)
                //   .WithMessage("Please select 'Yes' for the Private formal health sector as decedent health systems are marked as 'Yes' else select 'No' for all descendent health systems");
                //});

                //RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true);
                //RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true);
                //RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true);
                //RuleFor(x => x.HealthSector.PrivateFormal.PrivateFormal.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.HealthFacility.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.Hospital.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Hospital.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Hospital.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.Hospital.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Hospital.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.Hospital.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.Laboratory.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Laboratory.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.Laboratory.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.FaithBasedClinic.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.NGOClinic.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.Military.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.Military.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Military.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.Military.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Military.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.Military.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Military.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.Military.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.Police.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.Police.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Police.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.Police.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Police.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.Police.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Police.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.Police.ReportingMalaria == true);

                // RuleFor(x => x.HealthSector.PrivateFormal.Prison.National).NotEmpty().When(z => z.HealthSector.PrivateFormal.Prison.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Prison.SubNational).NotEmpty().When(z => z.HealthSector.PrivateFormal.Prison.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Prison.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateFormal.Prison.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateFormal.Prison.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateFormal.Prison.ReportingMalaria == true);
                #endregion

                #region Private informal
                RuleFor(x => x.HealthSector.PrivateInformal.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.PrivateInformal.MandatedToReport).NotNull();
                // RuleFor(x => x.HealthSector.PrivateInformal.National).NotEmpty().When(z => z.HealthSector.PrivateInformal.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateInformal.SubNational).NotEmpty().When(z => z.HealthSector.PrivateInformal.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateInformal.ServiceDelivery).NotEmpty().When(z => z.HealthSector.PrivateInformal.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.PrivateInformal.NotificationProcesses).NotEmpty().When(z => z.HealthSector.PrivateInformal.ReportingMalaria == true);
                #endregion

                #region Community
                RuleFor(x => x.HealthSector.Community.ReportingMalaria).NotNull();
                RuleFor(x => x.HealthSector.Community.MandatedToReport).NotNull();
                // RuleFor(x => x.HealthSector.Community.National).NotEmpty().When(z => z.HealthSector.Community.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.Community.SubNational).NotEmpty().When(z => z.HealthSector.Community.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.Community.ServiceDelivery).NotEmpty().When(z => z.HealthSector.Community.ReportingMalaria == true);
                // RuleFor(x => x.HealthSector.Community.NotificationProcesses).NotEmpty().When(z => z.HealthSector.Community.ReportingMalaria == true);
                #endregion
            });
        }
    }
}
